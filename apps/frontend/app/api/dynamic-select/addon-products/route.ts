import { NextRequest, NextResponse } from "next/server";
import { getToken } from "@flinkk/shared-auth/token";
import { prisma } from "@flinkk/database/prisma";
import { FlinkkInventoryAPI } from "@flinkk/inventory-api";

export const dynamic = "force-dynamic";

// POST /api/dynamic-select/addon-products - Get addon products/services from inventory service
export async function POST(req: NextRequest) {
  try {
    const { tenantId } = await getToken({ req });

    // Get inventory configuration for the tenant
    const inventoryConfig = await prisma.inventoryConfiguration.findUnique({
      where: {
        tenantId,
      },
      select: {
        apiUrl: true,
        token: true,
        isActive: true,
        verificationStatus: true,
      },
    });

    // Check if inventory is configured and active
    if (!inventoryConfig || !inventoryConfig.apiUrl || !inventoryConfig.token) {
      return NextResponse.json([]);
    }

    // Parse request body for category ID
    const body = await req.json();
    const { categoryId } = body || {};

    if (!categoryId) {
      return NextResponse.json([]);
    }

    // Create inventory API instance
    const inventoryAPI = new FlinkkInventoryAPI({
      apiUrl: inventoryConfig.apiUrl,
      token: inventoryConfig.token,
    });

    // Get products/services using the API
    const data = await inventoryAPI.getProductsServices({
      category_id: categoryId,
      limit: 100,
    });

    console.log("API Response:", data);

    // Transform products to DynamicSelect option format
    // The API returns ProductService[] directly, not wrapped in products_services
    const options = data?.products_services?.map((product: any) => ({
      value: product.id || product._id,
      label: product.name || product.title,
      description: product.description,
      categoryId: product.category_id,
      price: product.price,
      product_id: product.product_id,
      product_variant_id: product.product_variant_id,
      productData: product,
    })) || [];

    return NextResponse.json(options);
  } catch (error) {
    console.error(
      "Error fetching addon products from inventory service:",
      error,
    );
    return NextResponse.json([]);
  }
}
